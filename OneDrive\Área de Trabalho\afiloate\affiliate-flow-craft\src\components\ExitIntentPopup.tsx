// Exit Intent Popup for Maximum Conversion Recovery
import React, { useState, useEffect } from 'react';
import { X, Clock, Users, TrendingUp } from 'lucide-react';
import LeadCaptureForm from './LeadCaptureForm';

interface ExitIntentPopupProps {
  isVisible: boolean;
  onClose: () => void;
  onSuccess?: (data: any) => void;
}

const ExitIntentPopup: React.FC<ExitIntentPopupProps> = ({
  isVisible,
  onClose,
  onSuccess
}) => {
  const [timeLeft, setTimeLeft] = useState(15 * 60); // 15 minutes
  const [showForm, setShowForm] = useState(false);

  // Countdown timer
  useEffect(() => {
    if (!isVisible) return;

    const timer = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [isVisible]);

  // Format time
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/80 backdrop-blur-sm animate-fade-in"
        onClick={onClose}
      />
      
      {/* Popup Content */}
      <div className="relative z-10 max-w-2xl w-full max-h-[90vh] overflow-y-auto animate-scale-in">
        <div className="glass-dark rounded-3xl p-8 relative">
          {/* Close Button */}
          <button
            onClick={onClose}
            className="absolute top-6 right-6 w-8 h-8 rounded-full bg-white/10 hover:bg-white/20 flex items-center justify-center transition-colors"
          >
            <X className="w-4 h-4 text-lp-light" />
          </button>

          {!showForm ? (
            <>
              {/* Header */}
              <div className="text-center mb-8">
                <div className="text-6xl mb-4">🚨</div>
                <h2 className="text-4xl font-bold text-lp-light mb-4">
                  ESPERE! Não Vá Embora...
                </h2>
                <p className="text-xl text-lp-light/80 mb-6">
                  Você está a <span className="gradient-text-secondary font-bold">1 CLIQUE</span> de descobrir o sistema que já gerou
                </p>
                <div className="text-3xl font-bold gradient-text-primary mb-2">
                  R$ 2.847.000
                </div>
                <p className="text-lp-light/70">em comissões para nossos afiliados</p>
              </div>

              {/* Urgency Timer */}
              <div className="bg-gradient-to-r from-red-500/20 to-orange-500/20 rounded-2xl p-6 mb-8 text-center">
                <div className="flex items-center justify-center gap-2 mb-3">
                  <Clock className="w-5 h-5 text-red-400" />
                  <span className="text-red-400 font-semibold">OFERTA EXPIRA EM:</span>
                </div>
                <div className="text-4xl font-bold text-white mb-2">
                  {formatTime(timeLeft)}
                </div>
                <div className="text-sm text-lp-light/70">
                  Após esse tempo, você perderá acesso aos bônus exclusivos
                </div>
              </div>

              {/* Social Proof */}
              <div className="grid grid-cols-3 gap-4 mb-8">
                <div className="text-center">
                  <div className="w-12 h-12 gradient-primary rounded-xl flex items-center justify-center mx-auto mb-2">
                    <Users className="w-6 h-6 text-white" />
                  </div>
                  <div className="text-2xl font-bold text-lp-light">2.847</div>
                  <div className="text-xs text-lp-light/60">Afiliados Ativos</div>
                </div>
                
                <div className="text-center">
                  <div className="w-12 h-12 gradient-secondary rounded-xl flex items-center justify-center mx-auto mb-2">
                    <TrendingUp className="w-6 h-6 text-white" />
                  </div>
                  <div className="text-2xl font-bold text-lp-light">R$ 15k</div>
                  <div className="text-xs text-lp-light/60">Média Mensal</div>
                </div>
                
                <div className="text-center">
                  <div className="w-12 h-12 gradient-primary rounded-xl flex items-center justify-center mx-auto mb-2">
                    <Clock className="w-6 h-6 text-white" />
                  </div>
                  <div className="text-2xl font-bold text-lp-light">24h</div>
                  <div className="text-xs text-lp-light/60">Primeira Comissão</div>
                </div>
              </div>

              {/* Exclusive Bonuses */}
              <div className="bg-gradient-to-r from-lp-purple/10 to-lp-blue/10 rounded-2xl p-6 mb-8">
                <h3 className="text-xl font-bold text-lp-light mb-4 text-center">
                  🎁 BÔNUS EXCLUSIVOS (Valor: R$ 2.497)
                </h3>
                <div className="space-y-3 text-sm text-lp-light/80">
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-lp-green rounded-full"></div>
                    <span><strong>E-book Premium:</strong> "7 Segredos dos Afiliados Milionários" (R$ 497)</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-lp-orange rounded-full"></div>
                    <span><strong>Scripts Prontos:</strong> 50+ templates para redes sociais (R$ 697)</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-lp-purple rounded-full"></div>
                    <span><strong>Planilha Exclusiva:</strong> Controle de comissões automatizado (R$ 297)</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-lp-blue rounded-full"></div>
                    <span><strong>Vídeo Treinamento:</strong> "Primeira venda em 24h" (R$ 497)</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-lp-green rounded-full"></div>
                    <span><strong>Grupo VIP:</strong> Telegram exclusivo com suporte 24/7 (R$ 497)</span>
                  </div>
                </div>
              </div>

              {/* Testimonials */}
              <div className="mb-8">
                <h3 className="text-lg font-bold text-lp-light mb-4 text-center">
                  💬 O que nossos afiliados estão dizendo:
                </h3>
                <div className="space-y-4">
                  <div className="glass rounded-2xl p-4">
                    <p className="text-sm text-lp-light/80 mb-2">
                      "Em 30 dias faturei R$ 12.847 só com indicações! Método simples que realmente funciona."
                    </p>
                    <div className="text-xs text-lp-light/60">- Ana Paula, São Paulo</div>
                  </div>
                  
                  <div className="glass rounded-2xl p-4">
                    <p className="text-sm text-lp-light/80 mb-2">
                      "Nunca pensei que fosse tão fácil. R$ 15.200 em 45 dias sem vender nada!"
                    </p>
                    <div className="text-xs text-lp-light/60">- Carlos Silva, Rio de Janeiro</div>
                  </div>
                </div>
              </div>

              {/* CTA Button */}
              <div className="text-center">
                <button
                  onClick={() => setShowForm(true)}
                  className="btn-primary text-xl px-12 py-6 rounded-2xl shadow-2xl transform transition-all duration-300 hover:scale-105 active:scale-95 animate-pulse-glow"
                >
                  <span className="flex items-center gap-3">
                    🚀 SIM! QUERO GARANTIR MEUS BÔNUS
                  </span>
                </button>
                
                <p className="text-xs text-lp-light/50 mt-4">
                  ⚡ Últimas 7 vagas disponíveis
                </p>
              </div>

              {/* Risk Reversal */}
              <div className="text-center mt-6 text-xs text-lp-light/60">
                🔒 100% Seguro • ✅ Sem Compromisso • 🎁 Bônus Garantidos
              </div>
            </>
          ) : (
            <LeadCaptureForm
              variant="exit-intent"
              source="exit-intent-popup"
              onSuccess={onSuccess}
              onClose={onClose}
              showBonuses={false}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default ExitIntentPopup;
