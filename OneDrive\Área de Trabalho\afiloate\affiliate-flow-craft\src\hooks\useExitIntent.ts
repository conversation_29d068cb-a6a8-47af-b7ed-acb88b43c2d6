// Exit Intent Detection Hook
import { useState, useEffect, useCallback } from 'react';

interface ExitIntentOptions {
  threshold?: number;
  delay?: number;
  onlyOnce?: boolean;
  cookieExpiry?: number; // days
}

interface ExitIntentState {
  isTriggered: boolean;
  showPopup: boolean;
  triggerCount: number;
}

export const useExitIntent = (options: ExitIntentOptions = {}) => {
  const {
    threshold = 20,
    delay = 1000,
    onlyOnce = true,
    cookieExpiry = 1
  } = options;

  const [state, setState] = useState<ExitIntentState>({
    isTriggered: false,
    showPopup: false,
    triggerCount: 0
  });

  // Check if exit intent was already triggered
  const checkCookie = useCallback(() => {
    if (typeof window === 'undefined') return false;
    
    const cookie = document.cookie
      .split('; ')
      .find(row => row.startsWith('exitIntentTriggered='));
    
    return cookie ? cookie.split('=')[1] === 'true' : false;
  }, []);

  // Set cookie to remember exit intent was triggered
  const setCookie = useCallback(() => {
    if (typeof window === 'undefined') return;
    
    const expiryDate = new Date();
    expiryDate.setDate(expiryDate.getDate() + cookieExpiry);
    
    document.cookie = `exitIntentTriggered=true; expires=${expiryDate.toUTCString()}; path=/`;
  }, [cookieExpiry]);

  // Handle mouse leave event
  const handleMouseLeave = useCallback((e: MouseEvent) => {
    // Only trigger if mouse is leaving from the top of the page
    if (e.clientY <= threshold && e.relatedTarget === null) {
      setState(prev => {
        // Don't trigger if already triggered and onlyOnce is true
        if (onlyOnce && (prev.isTriggered || checkCookie())) {
          return prev;
        }

        // Add delay before showing popup
        setTimeout(() => {
          setState(current => ({
            ...current,
            showPopup: true
          }));
        }, delay);

        return {
          ...prev,
          isTriggered: true,
          triggerCount: prev.triggerCount + 1
        };
      });

      // Set cookie if onlyOnce is enabled
      if (onlyOnce) {
        setCookie();
      }
    }
  }, [threshold, delay, onlyOnce, checkCookie, setCookie]);

  // Handle scroll-based exit intent (mobile)
  const handleScroll = useCallback(() => {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollHeight = document.documentElement.scrollHeight;
    const clientHeight = document.documentElement.clientHeight;
    
    // Trigger when user scrolls to 80% of the page
    const scrollPercentage = (scrollTop + clientHeight) / scrollHeight;
    
    if (scrollPercentage >= 0.8) {
      setState(prev => {
        if (onlyOnce && (prev.isTriggered || checkCookie())) {
          return prev;
        }

        setTimeout(() => {
          setState(current => ({
            ...current,
            showPopup: true
          }));
        }, delay);

        return {
          ...prev,
          isTriggered: true,
          triggerCount: prev.triggerCount + 1
        };
      });

      if (onlyOnce) {
        setCookie();
      }
    }
  }, [delay, onlyOnce, checkCookie, setCookie]);

  // Time-based trigger (after user spends certain time on page)
  const handleTimeBasedTrigger = useCallback(() => {
    const timeThreshold = 30000; // 30 seconds
    
    setTimeout(() => {
      setState(prev => {
        if (onlyOnce && (prev.isTriggered || checkCookie())) {
          return prev;
        }

        // Only trigger if user is still active (not idle)
        if (document.hasFocus()) {
          setTimeout(() => {
            setState(current => ({
              ...current,
              showPopup: true
            }));
          }, delay);

          if (onlyOnce) {
            setCookie();
          }

          return {
            ...prev,
            isTriggered: true,
            triggerCount: prev.triggerCount + 1
          };
        }

        return prev;
      });
    }, timeThreshold);
  }, [delay, onlyOnce, checkCookie, setCookie]);

  // Setup event listeners
  useEffect(() => {
    // Skip if already triggered and onlyOnce is enabled
    if (onlyOnce && checkCookie()) {
      return;
    }

    // Mouse leave detection (desktop)
    document.addEventListener('mouseleave', handleMouseLeave);
    
    // Scroll detection (mobile)
    window.addEventListener('scroll', handleScroll, { passive: true });
    
    // Time-based trigger
    handleTimeBasedTrigger();

    return () => {
      document.removeEventListener('mouseleave', handleMouseLeave);
      window.removeEventListener('scroll', handleScroll);
    };
  }, [handleMouseLeave, handleScroll, handleTimeBasedTrigger, onlyOnce, checkCookie]);

  // Close popup
  const closePopup = useCallback(() => {
    setState(prev => ({
      ...prev,
      showPopup: false
    }));
  }, []);

  // Reset state (for testing)
  const reset = useCallback(() => {
    setState({
      isTriggered: false,
      showPopup: false,
      triggerCount: 0
    });
    
    // Clear cookie
    if (typeof window !== 'undefined') {
      document.cookie = 'exitIntentTriggered=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    }
  }, []);

  return {
    ...state,
    closePopup,
    reset
  };
};

// Additional hook for scroll-based triggers
export const useScrollTrigger = (threshold: number = 50) => {
  const [isTriggered, setIsTriggered] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const scrollHeight = document.documentElement.scrollHeight;
      const clientHeight = document.documentElement.clientHeight;
      
      const scrollPercentage = ((scrollTop + clientHeight) / scrollHeight) * 100;
      
      if (scrollPercentage >= threshold && !isTriggered) {
        setIsTriggered(true);
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [threshold, isTriggered]);

  return isTriggered;
};

// Hook for time-based triggers
export const useTimeTrigger = (delay: number = 30000) => {
  const [isTriggered, setIsTriggered] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsTriggered(true);
    }, delay);

    return () => clearTimeout(timer);
  }, [delay]);

  return isTriggered;
};
