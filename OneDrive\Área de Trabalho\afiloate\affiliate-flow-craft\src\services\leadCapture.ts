// Lead Capture Service with EmailJS Integration
import emailjs from '@emailjs/browser';

// EmailJS Configuration
const EMAILJS_SERVICE_ID = import.meta.env.VITE_EMAILJS_SERVICE_ID || 'your_service_id';
const EMAILJS_TEMPLATE_ID = import.meta.env.VITE_EMAILJS_TEMPLATE_ID || 'your_template_id';
const EMAILJS_PUBLIC_KEY = import.meta.env.VITE_EMAILJS_PUBLIC_KEY || 'your_public_key';

// Initialize EmailJS
emailjs.init(EMAILJS_PUBLIC_KEY);

export interface LeadData {
  name: string;
  email: string;
  phone?: string;
  source?: string;
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
}

export interface LeadCaptureResponse {
  success: boolean;
  message: string;
  data?: {
    leadId: string;
    affiliateLink: string;
    emailSent: boolean;
  };
  error?: string;
}

// Capture lead with full automation
export async function captureLeadWithAutomation(leadData: LeadData): Promise<LeadCaptureResponse> {
  try {
    // Add UTM parameters from URL
    const urlParams = new URLSearchParams(window.location.search);
    const enrichedLeadData = {
      ...leadData,
      utm_source: leadData.utm_source || urlParams.get('utm_source') || '',
      utm_medium: leadData.utm_medium || urlParams.get('utm_medium') || '',
      utm_campaign: leadData.utm_campaign || urlParams.get('utm_campaign') || '',
      source: leadData.source || 'landing-page'
    };

    // Send to Netlify Function for backend processing
    const backendResponse = await fetch('/.netlify/functions/capture-lead', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(enrichedLeadData)
    });

    const backendResult = await backendResponse.json();

    if (!backendResponse.ok) {
      throw new Error(backendResult.error || 'Erro no servidor');
    }

    // Send welcome email via EmailJS (client-side)
    await sendWelcomeEmail(enrichedLeadData);

    // Track conversion event
    trackConversion(enrichedLeadData);

    return {
      success: true,
      message: 'Lead capturado com sucesso! Verifique seu email.',
      data: backendResult.data
    };

  } catch (error) {
    console.error('Lead capture error:', error);
    
    return {
      success: false,
      message: 'Erro ao processar solicitação. Tente novamente.',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}

// Send welcome email via EmailJS
async function sendWelcomeEmail(leadData: LeadData): Promise<void> {
  try {
    const templateParams = {
      to_name: leadData.name,
      to_email: leadData.email,
      from_name: 'AffiliateFlow Premium',
      subject: '🚀 Bem-vindo ao AffiliateFlow Premium!',
      message: `
        Olá ${leadData.name}!
        
        Parabéns por dar o primeiro passo rumo à sua independência financeira!
        
        Seu link de afiliado está sendo preparado e você receberá todas as instruções em breve.
        
        Enquanto isso, prepare-se para descobrir:
        ✅ Como gerar até R$ 15.000/mês em comissões
        ✅ Estratégias de afiliados que realmente funcionam
        ✅ Acesso ao sistema mais avançado do mercado
        
        Fique atento ao seu email - em breve você receberá seu acesso completo!
        
        Atenciosamente,
        Equipe AffiliateFlow Premium
      `
    };

    await emailjs.send(EMAILJS_SERVICE_ID, EMAILJS_TEMPLATE_ID, templateParams);
    
  } catch (error) {
    console.error('Email sending error:', error);
    // Don't throw error - email is not critical for lead capture
  }
}

// Track conversion for analytics
function trackConversion(leadData: LeadData): void {
  try {
    // Google Analytics 4 Event
    if (typeof gtag !== 'undefined') {
      gtag('event', 'lead_capture', {
        event_category: 'conversion',
        event_label: leadData.source,
        value: 1
      });
    }

    // Facebook Pixel Event
    if (typeof fbq !== 'undefined') {
      fbq('track', 'Lead', {
        content_name: 'AffiliateFlow Premium',
        content_category: 'Lead Generation',
        value: 1,
        currency: 'BRL'
      });
    }

    // Custom event for internal tracking
    window.dispatchEvent(new CustomEvent('leadCaptured', {
      detail: {
        email: leadData.email,
        source: leadData.source,
        timestamp: new Date().toISOString()
      }
    }));

  } catch (error) {
    console.error('Tracking error:', error);
  }
}

// Validate email format
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Validate phone format (Brazilian)
export function validatePhone(phone: string): boolean {
  const phoneRegex = /^(\+55\s?)?(\(?\d{2}\)?\s?)?\d{4,5}-?\d{4}$/;
  return phoneRegex.test(phone.replace(/\s/g, ''));
}

// Format phone number
export function formatPhone(phone: string): string {
  const cleaned = phone.replace(/\D/g, '');
  
  if (cleaned.length === 11) {
    return cleaned.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
  } else if (cleaned.length === 10) {
    return cleaned.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
  }
  
  return phone;
}

// Get user's location for better targeting
export async function getUserLocation(): Promise<{ country?: string; region?: string; city?: string }> {
  try {
    const response = await fetch('https://ipapi.co/json/');
    const data = await response.json();
    
    return {
      country: data.country_name,
      region: data.region,
      city: data.city
    };
  } catch (error) {
    console.error('Location detection error:', error);
    return {};
  }
}
