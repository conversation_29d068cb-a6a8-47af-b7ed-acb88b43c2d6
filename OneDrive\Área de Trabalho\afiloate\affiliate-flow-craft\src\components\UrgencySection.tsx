// Urgency and Scarcity Section for Maximum Conversion
import React, { useState, useEffect } from 'react';
import { Clock, Users, TrendingUp, Zap, AlertTriangle, CheckCircle } from 'lucide-react';
import LeadCaptureForm from './LeadCaptureForm';

interface UrgencySectionProps {
  onLeadCaptureSuccess?: (data: any) => void;
}

const UrgencySection: React.FC<UrgencySectionProps> = ({ onLeadCaptureSuccess }) => {
  const [timeLeft, setTimeLeft] = useState(24 * 60 * 60); // 24 hours in seconds
  const [spotsLeft, setSpotsLeft] = useState(7);
  const [recentSignups, setRecentSignups] = useState([
    { name: 'Ana P.', location: 'São Paulo', time: '2 min atrás' },
    { name: 'Carlos M.', location: 'Rio de Janeiro', time: '5 min atrás' },
    { name: 'Mariana S.', location: 'Belo Horizonte', time: '8 min atrás' }
  ]);

  // Countdown timer
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          // Reset to 24 hours when it reaches 0
          return 24 * 60 * 60;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Simulate spots decreasing
  useEffect(() => {
    const spotsTimer = setInterval(() => {
      setSpotsLeft(prev => {
        const newSpots = Math.max(1, prev - Math.floor(Math.random() * 2));
        return newSpots;
      });
    }, 5 * 60 * 1000); // Every 5 minutes

    return () => clearInterval(spotsTimer);
  }, []);

  // Simulate recent signups
  useEffect(() => {
    const names = ['João S.', 'Maria L.', 'Pedro R.', 'Ana C.', 'Lucas M.', 'Fernanda P.'];
    const locations = ['São Paulo', 'Rio de Janeiro', 'Belo Horizonte', 'Brasília', 'Salvador', 'Fortaleza'];
    
    const signupTimer = setInterval(() => {
      const randomName = names[Math.floor(Math.random() * names.length)];
      const randomLocation = locations[Math.floor(Math.random() * locations.length)];
      
      setRecentSignups(prev => [
        { name: randomName, location: randomLocation, time: 'agora' },
        ...prev.slice(0, 2).map(signup => ({
          ...signup,
          time: signup.time === 'agora' ? '1 min atrás' : 
                signup.time === '1 min atrás' ? '3 min atrás' : '5 min atrás'
        }))
      ]);
    }, 30000); // Every 30 seconds

    return () => clearInterval(signupTimer);
  }, []);

  // Format time
  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    return {
      hours: hours.toString().padStart(2, '0'),
      minutes: minutes.toString().padStart(2, '0'),
      seconds: secs.toString().padStart(2, '0')
    };
  };

  const time = formatTime(timeLeft);

  return (
    <section className="py-20 relative overflow-hidden bg-gradient-to-b from-lp-navy to-lp-gray">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-[800px] h-[800px] gradient-primary rounded-full opacity-5 blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 gradient-secondary rounded-full opacity-10 blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Urgency Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-3 bg-red-500/20 border border-red-500/30 rounded-full px-6 py-3 mb-6">
            <AlertTriangle className="w-5 h-5 text-red-400" />
            <span className="text-sm font-medium text-red-400">ATENÇÃO: Oferta por tempo limitado</span>
          </div>
          
          <h2 className="text-5xl md:text-6xl font-bold text-lp-light mb-6">
            ⚡ Últimas <span className="gradient-text-secondary">{spotsLeft} Vagas</span>
            <br />
            <span className="gradient-text-primary">Disponíveis Hoje</span>
          </h2>
          
          <p className="text-xl text-lp-light/80 max-w-3xl mx-auto mb-8">
            Para manter a qualidade do suporte e garantir o sucesso de todos os afiliados, 
            limitamos as vagas diárias. <strong>Não perca sua chance!</strong>
          </p>
        </div>

        {/* Countdown Timer */}
        <div className="max-w-2xl mx-auto mb-12">
          <div className="glass-dark rounded-3xl p-8 text-center">
            <div className="flex items-center justify-center gap-2 mb-4">
              <Clock className="w-6 h-6 text-red-400" />
              <span className="text-red-400 font-semibold text-lg">OFERTA EXPIRA EM:</span>
            </div>
            
            <div className="grid grid-cols-3 gap-4 mb-6">
              <div className="bg-gradient-to-b from-red-500 to-red-600 rounded-2xl p-4">
                <div className="text-4xl font-bold text-white">{time.hours}</div>
                <div className="text-sm text-red-100">HORAS</div>
              </div>
              <div className="bg-gradient-to-b from-red-500 to-red-600 rounded-2xl p-4">
                <div className="text-4xl font-bold text-white">{time.minutes}</div>
                <div className="text-sm text-red-100">MINUTOS</div>
              </div>
              <div className="bg-gradient-to-b from-red-500 to-red-600 rounded-2xl p-4">
                <div className="text-4xl font-bold text-white">{time.seconds}</div>
                <div className="text-sm text-red-100">SEGUNDOS</div>
              </div>
            </div>
            
            <p className="text-sm text-lp-light/70">
              Após esse prazo, você terá que aguardar a próxima abertura de vagas
            </p>
          </div>
        </div>

        {/* Social Proof - Recent Signups */}
        <div className="max-w-md mx-auto mb-12">
          <div className="glass-dark rounded-2xl p-6">
            <div className="flex items-center gap-2 mb-4">
              <div className="w-3 h-3 bg-lp-green rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-lp-light">Pessoas se cadastrando agora:</span>
            </div>
            
            <div className="space-y-3">
              {recentSignups.map((signup, index) => (
                <div key={index} className="flex items-center justify-between text-sm">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-lp-orange rounded-full"></div>
                    <span className="text-lp-light/80">{signup.name} - {signup.location}</span>
                  </div>
                  <span className="text-lp-light/60">{signup.time}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Scarcity Indicators */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          <div className="glass-dark rounded-2xl p-6 text-center">
            <Users className="w-8 h-8 text-red-400 mx-auto mb-3" />
            <div className="text-2xl font-bold text-red-400 mb-2">{spotsLeft}</div>
            <div className="text-sm text-lp-light/70">Vagas restantes hoje</div>
          </div>
          
          <div className="glass-dark rounded-2xl p-6 text-center">
            <TrendingUp className="w-8 h-8 text-lp-orange mx-auto mb-3" />
            <div className="text-2xl font-bold text-lp-orange mb-2">847</div>
            <div className="text-sm text-lp-light/70">Pessoas na fila de espera</div>
          </div>
          
          <div className="glass-dark rounded-2xl p-6 text-center">
            <CheckCircle className="w-8 h-8 text-lp-green mx-auto mb-3" />
            <div className="text-2xl font-bold text-lp-green mb-2">2.847</div>
            <div className="text-sm text-lp-light/70">Afiliados já aprovados</div>
          </div>
        </div>

        {/* Final CTA with Lead Capture */}
        <div className="max-w-2xl mx-auto">
          <div className="text-center mb-8">
            <h3 className="text-3xl font-bold text-lp-light mb-4">
              🚨 Não Deixe Sua Vaga Escapar!
            </h3>
            <p className="text-lg text-lp-light/80 mb-6">
              Preencha os dados abaixo e garante sua vaga antes que seja tarde demais
            </p>
            
            {/* Bonus Reminder */}
            <div className="bg-gradient-to-r from-lp-orange/10 to-lp-green/10 rounded-2xl p-6 mb-8">
              <h4 className="text-lg font-bold text-lp-light mb-3">
                🎁 BÔNUS EXCLUSIVOS (Apenas hoje - Valor: R$ 2.497)
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm text-lp-light/80">
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-lp-green" />
                  <span>E-book: 7 Segredos dos Afiliados (R$ 497)</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-lp-green" />
                  <span>Scripts Prontos para Redes Sociais (R$ 697)</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-lp-green" />
                  <span>Planilha de Controle de Comissões (R$ 297)</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-lp-green" />
                  <span>Grupo VIP no Telegram (R$ 497)</span>
                </div>
              </div>
            </div>
          </div>

          <LeadCaptureForm
            variant="inline"
            source="urgency-section"
            onSuccess={onLeadCaptureSuccess}
            showBonuses={false}
          />
          
          <div className="text-center mt-6 text-sm text-lp-light/60">
            🔒 Seus dados estão 100% seguros • ⚡ Processo automatizado • 🎯 Sem compromisso
          </div>
        </div>
      </div>
    </section>
  );
};

export default UrgencySection;
