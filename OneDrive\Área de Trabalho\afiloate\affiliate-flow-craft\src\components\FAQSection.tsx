// FAQ Section optimized for conversion and objection handling
import React, { useState } from 'react';
import { ChevronDown, ChevronUp, HelpCircle, CheckCircle, DollarSign, Clock } from 'lucide-react';

interface FAQItem {
  question: string;
  answer: string;
  category: 'getting-started' | 'earnings' | 'support' | 'technical';
  highlight?: string;
}

const FAQSection: React.FC = () => {
  const [openItems, setOpenItems] = useState<number[]>([0]); // First item open by default

  const faqs: FAQItem[] = [
    {
      question: '💰 Quanto posso ganhar realmente com o AffiliateFlow?',
      answer: 'Nossos afiliados ganham entre R$ 2.000 a R$ 15.000 por mês. A média é de R$ 8.500/mês após os primeiros 60 dias. Seus ganhos dependem do seu esforço e dedicação, mas temos afiliados que faturam mais de R$ 50.000/mês.',
      category: 'earnings',
      highlight: 'R$ 8.500/mês em média'
    },
    {
      question: '⏰ Em quanto tempo vou receber minha primeira comissão?',
      answer: 'A maioria dos nossos afiliados recebe a primeira comissão entre 24-48h após a primeira indicação bem-sucedida. Temos casos de pessoas que receberam no mesmo dia! O sistema é automatizado e os pagamentos são processados rapidamente.',
      category: 'earnings',
      highlight: '24-48h para primeira comissão'
    },
    {
      question: '🤔 Preciso ter experiência em vendas ou marketing?',
      answer: 'Não! Nosso sistema foi criado especialmente para iniciantes. Fornecemos todos os materiais, scripts prontos, treinamentos em vídeo e suporte completo. Você só precisa seguir o passo a passo que ensinamos.',
      category: 'getting-started',
      highlight: 'Zero experiência necessária'
    },
    {
      question: '💸 Preciso investir dinheiro para começar?',
      answer: 'Absolutamente não! O AffiliateFlow é 100% gratuito para começar. Você não paga nada para se tornar afiliado. Só ganha quando faz indicações bem-sucedidas. É risco zero para você.',
      category: 'getting-started',
      highlight: 'R$ 0 de investimento inicial'
    },
    {
      question: '📱 Como funciona exatamente o sistema?',
      answer: 'É muito simples: 1) Você recebe seu link de afiliado exclusivo, 2) Compartilha com pessoas que podem se beneficiar da IA, 3) Quando alguém se cadastra através do seu link, você ganha comissão, 4) O sistema rastreia tudo automaticamente e você acompanha em tempo real.',
      category: 'technical',
      highlight: '4 passos simples'
    },
    {
      question: '🎯 Qual é o público-alvo ideal para indicar?',
      answer: 'Empresários, empreendedores, profissionais liberais, consultores, agências de marketing, e-commerces, startups - qualquer pessoa ou empresa que queira usar IA para crescer. O mercado é gigantesco!',
      category: 'getting-started',
      highlight: 'Mercado de milhões de empresas'
    },
    {
      question: '📞 Que tipo de suporte vocês oferecem?',
      answer: 'Suporte completo 24/7! Temos grupo VIP no Telegram, materiais de treinamento, scripts prontos, webinars semanais, e uma equipe dedicada para tirar todas as suas dúvidas. Você nunca estará sozinho.',
      category: 'support',
      highlight: 'Suporte 24/7 completo'
    },
    {
      question: '💳 Como e quando recebo os pagamentos?',
      answer: 'Os pagamentos são feitos semanalmente via PIX ou transferência bancária. Você pode acompanhar suas comissões em tempo real no painel do afiliado. Pagamento mínimo de R$ 100.',
      category: 'earnings',
      highlight: 'Pagamentos semanais via PIX'
    },
    {
      question: '📊 Posso acompanhar meus resultados?',
      answer: 'Sim! Você tem acesso a um painel completo onde pode ver: cliques no seu link, cadastros realizados, comissões geradas, histórico de pagamentos, e muito mais. Tudo em tempo real!',
      category: 'technical',
      highlight: 'Painel completo em tempo real'
    },
    {
      question: '🚀 E se eu não conseguir resultados?',
      answer: 'Isso é praticamente impossível se você seguir nosso método. Mas se por algum motivo você não conseguir sua primeira comissão em 30 dias seguindo nossas orientações, nossa equipe fará uma consultoria individual gratuita para identificar e corrigir o problema.',
      category: 'support',
      highlight: 'Consultoria gratuita se necessário'
    }
  ];

  const toggleItem = (index: number) => {
    setOpenItems(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
  };

  const categoryIcons = {
    'getting-started': '🚀',
    'earnings': '💰',
    'support': '🎯',
    'technical': '⚙️'
  };

  return (
    <section className="py-20 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 right-10 w-72 h-72 gradient-primary rounded-full opacity-5 blur-3xl"></div>
        <div className="absolute bottom-20 left-10 w-96 h-96 gradient-secondary rounded-full opacity-5 blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-3 glass rounded-full px-6 py-3 mb-6">
            <HelpCircle className="w-5 h-5 text-lp-orange" />
            <span className="text-sm font-medium text-lp-light/90">Perguntas Frequentes</span>
          </div>
          
          <h2 className="text-5xl md:text-6xl font-bold text-lp-light mb-6">
            Tire Todas as Suas <span className="gradient-text-secondary">Dúvidas</span>
          </h2>
          
          <p className="text-xl text-lp-light/80 max-w-3xl mx-auto">
            Respostas claras e diretas para as perguntas mais comuns sobre o AffiliateFlow Premium
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
          <div className="glass-dark rounded-2xl p-6 text-center">
            <DollarSign className="w-8 h-8 gradient-primary rounded-xl p-1 mx-auto mb-3" />
            <div className="text-2xl font-bold gradient-text-primary">R$ 15k</div>
            <div className="text-sm text-lp-light/60">Máximo mensal</div>
          </div>
          
          <div className="glass-dark rounded-2xl p-6 text-center">
            <Clock className="w-8 h-8 gradient-secondary rounded-xl p-1 mx-auto mb-3" />
            <div className="text-2xl font-bold gradient-text-secondary">24h</div>
            <div className="text-sm text-lp-light/60">Primeira comissão</div>
          </div>
          
          <div className="glass-dark rounded-2xl p-6 text-center">
            <CheckCircle className="w-8 h-8 gradient-primary rounded-xl p-1 mx-auto mb-3" />
            <div className="text-2xl font-bold gradient-text-primary">100%</div>
            <div className="text-sm text-lp-light/60">Gratuito</div>
          </div>
          
          <div className="glass-dark rounded-2xl p-6 text-center">
            <HelpCircle className="w-8 h-8 gradient-secondary rounded-xl p-1 mx-auto mb-3" />
            <div className="text-2xl font-bold gradient-text-secondary">24/7</div>
            <div className="text-sm text-lp-light/60">Suporte</div>
          </div>
        </div>

        {/* FAQ Items */}
        <div className="max-w-4xl mx-auto">
          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <div
                key={index}
                className="glass-dark rounded-2xl overflow-hidden hover-lift transition-all duration-300"
              >
                <button
                  onClick={() => toggleItem(index)}
                  className="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-white/5 transition-colors"
                >
                  <div className="flex items-center gap-4">
                    <span className="text-2xl">
                      {categoryIcons[faq.category]}
                    </span>
                    <div>
                      <h3 className="text-lg font-semibold text-lp-light mb-1">
                        {faq.question}
                      </h3>
                      {faq.highlight && (
                        <div className="text-sm gradient-text-secondary font-medium">
                          {faq.highlight}
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex-shrink-0 ml-4">
                    {openItems.includes(index) ? (
                      <ChevronUp className="w-6 h-6 text-lp-light/60" />
                    ) : (
                      <ChevronDown className="w-6 h-6 text-lp-light/60" />
                    )}
                  </div>
                </button>
                
                {openItems.includes(index) && (
                  <div className="px-8 pb-6 animate-fade-in-up">
                    <div className="pl-12">
                      <p className="text-lp-light/80 leading-relaxed">
                        {faq.answer}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Still Have Questions CTA */}
        <div className="text-center mt-16">
          <div className="glass-dark rounded-3xl p-8 max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-lp-light mb-4">
              Ainda tem dúvidas? 🤔
            </h3>
            <p className="text-lp-light/80 mb-6">
              Nossa equipe está pronta para esclarecer qualquer questão e te ajudar a começar hoje mesmo!
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="btn-secondary px-8 py-4 rounded-2xl inline-flex items-center gap-3">
                📱 WhatsApp Direto
              </button>

              <button className="btn-outline px-8 py-4 rounded-2xl inline-flex items-center gap-3">
                ✉️ Email Suporte
              </button>
            </div>
            
            <div className="mt-6 text-sm text-lp-light/60">
              ⚡ Resposta em até 2 horas • 🎯 Suporte especializado
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FAQSection;
