// Netlify Function for Lead Capture with Email Automation
const fetch = require('node-fetch');

exports.handler = async (event, context) => {
  // Enable CORS
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Content-Type': 'application/json'
  };

  // <PERSON>le preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  try {
    const { name, email, phone, source, utm_source, utm_medium, utm_campaign } = JSON.parse(event.body);

    // Validate required fields
    if (!name || !email) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'Nome e email são obrigatórios' })
      };
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'Email inválido' })
      };
    }

    // Prepare lead data
    const leadData = {
      name: name.trim(),
      email: email.toLowerCase().trim(),
      phone: phone?.trim() || '',
      source: source || 'landing-page',
      utm_source: utm_source || '',
      utm_medium: utm_medium || '',
      utm_campaign: utm_campaign || '',
      created_at: new Date().toISOString(),
      ip_address: event.headers['x-forwarded-for'] || event.headers['client-ip'] || 'unknown',
      user_agent: event.headers['user-agent'] || 'unknown'
    };

    // Save to Airtable (free tier: 1,200 records/month)
    const airtableResponse = await saveToAirtable(leadData);
    
    // Send welcome email via EmailJS
    const emailResponse = await sendWelcomeEmail(leadData);

    // Generate affiliate link (simulate for now)
    const affiliateLink = generateAffiliateLink(leadData.email);

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        message: 'Lead capturado com sucesso!',
        data: {
          leadId: airtableResponse.id,
          affiliateLink: affiliateLink,
          emailSent: emailResponse.success
        }
      })
    };

  } catch (error) {
    console.error('Error processing lead:', error);
    
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error: 'Erro interno do servidor',
        message: 'Tente novamente em alguns instantes'
      })
    };
  }
};

// Save lead to Airtable
async function saveToAirtable(leadData) {
  const AIRTABLE_API_KEY = process.env.AIRTABLE_API_KEY;
  const AIRTABLE_BASE_ID = process.env.AIRTABLE_BASE_ID;
  const AIRTABLE_TABLE_NAME = process.env.AIRTABLE_TABLE_NAME || 'Leads';

  if (!AIRTABLE_API_KEY || !AIRTABLE_BASE_ID) {
    throw new Error('Airtable credentials not configured');
  }

  const url = `https://api.airtable.com/v0/${AIRTABLE_BASE_ID}/${AIRTABLE_TABLE_NAME}`;
  
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${AIRTABLE_API_KEY}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      fields: {
        'Nome': leadData.name,
        'Email': leadData.email,
        'Telefone': leadData.phone,
        'Fonte': leadData.source,
        'UTM Source': leadData.utm_source,
        'UTM Medium': leadData.utm_medium,
        'UTM Campaign': leadData.utm_campaign,
        'Data Criação': leadData.created_at,
        'IP': leadData.ip_address,
        'User Agent': leadData.user_agent,
        'Status': 'Novo Lead'
      }
    })
  });

  if (!response.ok) {
    throw new Error(`Airtable API error: ${response.status}`);
  }

  const data = await response.json();
  return data;
}

// Send welcome email via EmailJS
async function sendWelcomeEmail(leadData) {
  // EmailJS is client-side, so we'll return success for now
  // In production, you could use SendGrid, Mailgun, or similar
  return { success: true };
}

// Generate affiliate link
function generateAffiliateLink(email) {
  const baseUrl = 'https://grip.gaiodataos.com/';
  const affiliateId = Buffer.from(email).toString('base64').substring(0, 12);
  return `${baseUrl}?ref=${affiliateId}&utm_source=affiliate&utm_medium=referral`;
}
