# Netlify Configuration for High-Converting Landing Page
[build]
  publish = "dist"
  command = "npm run build"
  functions = "netlify/functions"

[build.environment]
  NODE_VERSION = "18"

# Redirect rules for SPA
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.emailjs.com https://www.google-analytics.com https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.emailjs.com https://api.airtable.com https://www.google-analytics.com;"

# Cache static assets
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Environment variables (set in Netlify dashboard)
# EMAILJS_SERVICE_ID = your_emailjs_service_id
# EMAILJS_TEMPLATE_ID = your_emailjs_template_id  
# EMAILJS_PUBLIC_KEY = your_emailjs_public_key
# AIRTABLE_API_KEY = your_airtable_api_key
# AIRTABLE_BASE_ID = your_airtable_base_id
# AIRTABLE_TABLE_NAME = Leads
