// Benefits Section with Social Proof and Value Proposition
import React from 'react';
import { 
  DollarSign, 
  Clock, 
  Users, 
  TrendingUp, 
  Shield, 
  Zap,
  Target,
  Award,
  CheckCircle
} from 'lucide-react';

const BenefitsSection: React.FC = () => {
  const benefits = [
    {
      icon: DollarSign,
      title: 'Renda Recorrente Garantida',
      description: 'Comissões de até R$ 15.000/mês com sistema 100% automatizado',
      highlight: 'R$ 15k/mês',
      color: 'gradient-primary'
    },
    {
      icon: Clock,
      title: 'Resultados em 24-48h',
      description: 'Primeira comissão em até 48h após sua primeira indicação',
      highlight: '24-48h',
      color: 'gradient-secondary'
    },
    {
      icon: Users,
      title: '<PERSON><PERSON><PERSON>',
      description: 'Milhares de empresas precisam dessa solução de IA',
      highlight: '+10M empresas',
      color: 'gradient-primary'
    },
    {
      icon: Shield,
      title: 'Zero Investimento',
      description: 'Comece hoje mesmo sem investir um centavo',
      highlight: 'R$ 0',
      color: 'gradient-secondary'
    },
    {
      icon: Target,
      title: 'Sistema Comprovado',
      description: 'Método testado por +2.847 afiliados ativos',
      highlight: '2.847 afiliados',
      color: 'gradient-primary'
    },
    {
      icon: Award,
      title: 'Suporte Completo',
      description: 'Materiais, treinamentos e suporte 24/7',
      highlight: '24/7',
      color: 'gradient-secondary'
    }
  ];

  const testimonials = [
    {
      name: 'Ana Paula Silva',
      location: 'São Paulo, SP',
      result: 'R$ 12.847',
      period: '30 dias',
      text: 'Em apenas 30 dias consegui faturar R$ 12.847 só com indicações! O método é simples e realmente funciona.',
      avatar: '👩‍💼'
    },
    {
      name: 'Carlos Mendes',
      location: 'Rio de Janeiro, RJ',
      result: 'R$ 8.500',
      period: '21 dias',
      text: 'Nunca pensei que fosse tão fácil. Em 21 dias já tinha R$ 8.500 na conta. Recomendo para todos!',
      avatar: '👨‍💻'
    },
    {
      name: 'Mariana Costa',
      location: 'Belo Horizonte, MG',
      result: 'R$ 15.200',
      period: '45 dias',
      text: 'Superou todas as minhas expectativas. R$ 15.200 em 45 dias trabalhando apenas 2h por dia.',
      avatar: '👩‍🎓'
    }
  ];

  return (
    <section className="py-20 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-72 h-72 gradient-primary rounded-full opacity-5 blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 gradient-secondary rounded-full opacity-5 blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-3 glass rounded-full px-6 py-3 mb-6">
            <TrendingUp className="w-5 h-5 text-lp-green" />
            <span className="text-sm font-medium text-lp-light/90">Por que escolher o AffiliateFlow?</span>
          </div>
          
          <h2 className="text-5xl md:text-6xl font-bold text-lp-light mb-6">
            Transforme <span className="gradient-text-secondary">Indicações</span> em
            <br />
            <span className="gradient-text-primary">Renda Recorrente</span>
          </h2>
          
          <p className="text-xl text-lp-light/80 max-w-3xl mx-auto">
            Descubra por que mais de 2.847 pessoas já escolheram nosso sistema para 
            gerar uma renda extra consistente todos os meses
          </p>
        </div>

        {/* Benefits Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {benefits.map((benefit, index) => (
            <div
              key={index}
              className="glass-dark rounded-3xl p-8 hover-lift group"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className={`w-16 h-16 ${benefit.color} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                <benefit.icon className="w-8 h-8 text-white" />
              </div>
              
              <div className="mb-4">
                <div className={`text-2xl font-bold mb-2 ${benefit.color.replace('gradient', 'gradient-text')}`}>
                  {benefit.highlight}
                </div>
                <h3 className="text-xl font-bold text-lp-light mb-3">
                  {benefit.title}
                </h3>
                <p className="text-lp-light/70 leading-relaxed">
                  {benefit.description}
                </p>
              </div>
              
              <div className="flex items-center gap-2 text-lp-green text-sm font-medium">
                <CheckCircle className="w-4 h-4" />
                <span>Comprovado e testado</span>
              </div>
            </div>
          ))}
        </div>

        {/* Social Proof Section */}
        <div className="text-center mb-12">
          <h3 className="text-3xl font-bold text-lp-light mb-4">
            💬 O que nossos afiliados estão dizendo
          </h3>
          <p className="text-lp-light/70 mb-12">
            Resultados reais de pessoas reais que transformaram suas vidas
          </p>
        </div>

        {/* Testimonials */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {testimonials.map((testimonial, index) => (
            <div
              key={index}
              className="glass-dark rounded-3xl p-8 hover-lift"
              style={{ animationDelay: `${index * 0.2}s` }}
            >
              <div className="flex items-center gap-4 mb-6">
                <div className="text-4xl">{testimonial.avatar}</div>
                <div>
                  <div className="font-bold text-lp-light">{testimonial.name}</div>
                  <div className="text-sm text-lp-light/60">{testimonial.location}</div>
                </div>
              </div>
              
              <div className="mb-6">
                <div className="flex items-center gap-2 mb-2">
                  <div className="text-2xl font-bold gradient-text-secondary">
                    {testimonial.result}
                  </div>
                  <div className="text-sm text-lp-light/60">
                    em {testimonial.period}
                  </div>
                </div>
                <p className="text-lp-light/80 leading-relaxed">
                  "{testimonial.text}"
                </p>
              </div>
              
              <div className="flex items-center gap-1">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="text-yellow-400">⭐</div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Stats Section */}
        <div className="glass-dark rounded-3xl p-8 md:p-12">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-lp-light mb-2">
              📊 Números que Impressionam
            </h3>
            <p className="text-lp-light/70">
              Resultados comprovados do nosso sistema
            </p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-4xl font-bold gradient-text-primary mb-2">
                R$ 2.8M
              </div>
              <div className="text-sm text-lp-light/70">
                Total pago em comissões
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-4xl font-bold gradient-text-secondary mb-2">
                2.847
              </div>
              <div className="text-sm text-lp-light/70">
                Afiliados ativos
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-4xl font-bold gradient-text-primary mb-2">
                4.9/5
              </div>
              <div className="text-sm text-lp-light/70">
                Avaliação média
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-4xl font-bold gradient-text-secondary mb-2">
                24h
              </div>
              <div className="text-sm text-lp-light/70">
                Primeira comissão
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default BenefitsSection;
