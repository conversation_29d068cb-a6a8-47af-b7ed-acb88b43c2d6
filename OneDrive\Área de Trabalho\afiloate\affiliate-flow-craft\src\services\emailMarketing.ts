// Advanced Email Marketing Automation System
import emailjs from '@emailjs/browser';

// Email Templates for Marketing Sequences
export const EMAIL_TEMPLATES = {
  WELCOME: {
    subject: '🚀 Bem-vindo ao AffiliateFlow Premium - Seu Sucesso Começa AGORA!',
    template: 'welcome_template'
  },
  ONBOARDING_DAY1: {
    subject: '💰 Como Ganhar Seus Primeiros R$ 1.000 em 7 Dias',
    template: 'onboarding_day1'
  },
  ONBOARDING_DAY3: {
    subject: '🎯 3 Estratégias Secretas dos Top Afiliados (R$ 50k/mês)',
    template: 'onboarding_day3'
  },
  ONBOARDING_DAY7: {
    subject: '⚡ Última Chance: Acesso VIP ao Método dos R$ 15k/mês',
    template: 'onboarding_day7'
  },
  CONVERSION_FOLLOW_UP: {
    subject: '🔥 Parabéns! Seu Link Está Ativo - Primeiros Passos',
    template: 'conversion_followup'
  },
  REACTIVATION: {
    subject: '😢 Sentimos Sua Falta... Oferta Especial Só Para Você!',
    template: 'reactivation'
  }
};

export interface EmailSequenceData {
  name: string;
  email: string;
  leadSource: string;
  affiliateLink?: string;
  customData?: Record<string, any>;
}

// Email Marketing Service Class
export class EmailMarketingService {
  private serviceId: string;
  private publicKey: string;

  constructor() {
    this.serviceId = import.meta.env.VITE_EMAILJS_SERVICE_ID || '';
    this.publicKey = import.meta.env.VITE_EMAILJS_PUBLIC_KEY || '';
    
    if (this.publicKey) {
      emailjs.init(this.publicKey);
    }
  }

  // Send welcome email with immediate value
  async sendWelcomeEmail(data: EmailSequenceData): Promise<boolean> {
    try {
      const templateParams = {
        to_name: data.name,
        to_email: data.email,
        from_name: 'Marcus Silva - AffiliateFlow',
        subject: EMAIL_TEMPLATES.WELCOME.subject,
        
        // Personalized content
        lead_source: data.leadSource,
        affiliate_link: data.affiliateLink || '',
        
        // Welcome message with immediate value
        welcome_message: this.getWelcomeMessage(data.name),
        
        // Bonus content
        bonus_content: this.getBonusContent(),
        
        // Next steps
        next_steps: this.getNextSteps(),
        
        // Social proof
        social_proof: this.getSocialProof()
      };

      await emailjs.send(this.serviceId, EMAIL_TEMPLATES.WELCOME.template, templateParams);
      
      // Schedule follow-up emails
      this.scheduleEmailSequence(data);
      
      return true;
    } catch (error) {
      console.error('Welcome email error:', error);
      return false;
    }
  }

  // Schedule automated email sequence
  private scheduleEmailSequence(data: EmailSequenceData): void {
    // Day 1: Onboarding and first strategy
    setTimeout(() => {
      this.sendOnboardingDay1(data);
    }, 24 * 60 * 60 * 1000); // 24 hours

    // Day 3: Advanced strategies
    setTimeout(() => {
      this.sendOnboardingDay3(data);
    }, 3 * 24 * 60 * 60 * 1000); // 3 days

    // Day 7: Final push with urgency
    setTimeout(() => {
      this.sendOnboardingDay7(data);
    }, 7 * 24 * 60 * 60 * 1000); // 7 days
  }

  // Day 1 onboarding email
  private async sendOnboardingDay1(data: EmailSequenceData): Promise<void> {
    try {
      const templateParams = {
        to_name: data.name,
        to_email: data.email,
        from_name: 'Marcus Silva - AffiliateFlow',
        subject: EMAIL_TEMPLATES.ONBOARDING_DAY1.subject,
        
        main_content: `
          Olá ${data.name}!
          
          Espero que esteja animado(a) para começar sua jornada rumo aos R$ 15.000/mês!
          
          Hoje vou compartilhar com você a PRIMEIRA estratégia que mudou a vida de mais de 2.847 afiliados:
          
          🎯 **ESTRATÉGIA #1: O Método da Indicação Inteligente**
          
          Ao invés de "vender" diretamente, você vai EDUCAR seu público sobre:
          ✅ Como a IA está revolucionando empresas
          ✅ Por que grandes corporações estão investindo bilhões
          ✅ Como pequenas empresas podem competir usando as mesmas ferramentas
          
          **AÇÃO PARA HOJE:**
          1. Acesse seu painel de afiliado: ${data.affiliateLink || '[LINK_SERÁ_ENVIADO]'}
          2. Baixe o kit de materiais exclusivos
          3. Compartilhe o primeiro conteúdo educativo
          
          **RESULTADO ESPERADO:** Suas primeiras comissões em 48-72h
          
          Amanhã vou revelar a Estratégia #2 que pode TRIPLICAR seus resultados...
          
          Forte abraço,
          Marcus Silva
          
          P.S.: Mais de 847 pessoas já estão aplicando essa estratégia HOJE. Não fique para trás!
        `,
        
        cta_text: 'ACESSAR PAINEL DE AFILIADO',
        cta_link: data.affiliateLink || '#'
      };

      await emailjs.send(this.serviceId, EMAIL_TEMPLATES.ONBOARDING_DAY1.template, templateParams);
    } catch (error) {
      console.error('Day 1 email error:', error);
    }
  }

  // Day 3 advanced strategies
  private async sendOnboardingDay3(data: EmailSequenceData): Promise<void> {
    try {
      const templateParams = {
        to_name: data.name,
        to_email: data.email,
        from_name: 'Marcus Silva - AffiliateFlow',
        subject: EMAIL_TEMPLATES.ONBOARDING_DAY3.subject,
        
        main_content: `
          ${data.name}, como estão os primeiros resultados?
          
          Espero que já tenha visto suas primeiras comissões chegando! 💰
          
          Hoje vou revelar as 3 ESTRATÉGIAS SECRETAS que os top afiliados (R$ 50k/mês) usam:
          
          🔥 **ESTRATÉGIA SECRETA #1: O Funil da Autoridade**
          - Como se posicionar como especialista em IA
          - Scripts prontos para redes sociais
          - Técnica do "Problema → Solução → Prova"
          
          ⚡ **ESTRATÉGIA SECRETA #2: O Método da Escassez Inteligente**
          - Como criar urgência sem ser "vendedor"
          - Gatilhos psicológicos que convertem 3x mais
          - Timing perfeito para máxima conversão
          
          🎯 **ESTRATÉGIA SECRETA #3: A Rede de Indicações Exponencial**
          - Como transformar 1 cliente em 10 indicações
          - Sistema de recompensas que funciona
          - Automação completa do processo
          
          **BÔNUS EXCLUSIVO:** Planilha de controle de comissões + Calculadora de metas
          
          Acesse agora: ${data.affiliateLink || '[LINK_PAINEL]'}
          
          Nos vemos no topo!
          Marcus Silva
        `,
        
        bonus_content: 'Planilha Exclusiva + Scripts Prontos',
        cta_text: 'BAIXAR ESTRATÉGIAS SECRETAS',
        cta_link: data.affiliateLink || '#'
      };

      await emailjs.send(this.serviceId, EMAIL_TEMPLATES.ONBOARDING_DAY3.template, templateParams);
    } catch (error) {
      console.error('Day 3 email error:', error);
    }
  }

  // Day 7 final push with urgency
  private async sendOnboardingDay7(data: EmailSequenceData): Promise<void> {
    try {
      const templateParams = {
        to_name: data.name,
        to_email: data.email,
        from_name: 'Marcus Silva - AffiliateFlow',
        subject: EMAIL_TEMPLATES.ONBOARDING_DAY7.subject,
        
        main_content: `
          ${data.name}, esta é sua ÚLTIMA CHANCE...
          
          Nos últimos 7 dias, você recebeu estratégias que valem mais de R$ 5.000.
          
          Mas hoje quero fazer uma oferta ESPECIAL só para você:
          
          🚨 **ACESSO VIP AO MÉTODO DOS R$ 15K/MÊS** 🚨
          
          ✅ Mentoria individual comigo (1h)
          ✅ Grupo VIP no Telegram (apenas 100 vagas)
          ✅ Scripts de conversão que faturam R$ 50k/mês
          ✅ Suporte prioritário 24/7
          ✅ Comissões DOBRADAS nos primeiros 30 dias
          
          **VALOR NORMAL:** R$ 2.997
          **SEU PREÇO HOJE:** R$ 497 (83% OFF)
          
          ⏰ **OFERTA EXPIRA EM 24 HORAS**
          
          Apenas 23 vagas restantes...
          
          Esta é SUA chance de sair do comum e entrar para o grupo dos R$ 15k/mês.
          
          Não deixe para amanhã o que pode mudar sua vida HOJE!
          
          [GARANTIR MINHA VAGA VIP]
          
          Último aviso,
          Marcus Silva
          
          P.S.: Se não aproveitar hoje, essa oferta não voltará. Decida AGORA!
        `,
        
        urgency_timer: '24:00:00',
        special_price: 'R$ 497',
        normal_price: 'R$ 2.997',
        discount: '83% OFF',
        cta_text: 'GARANTIR VAGA VIP AGORA',
        cta_link: data.affiliateLink || '#'
      };

      await emailjs.send(this.serviceId, EMAIL_TEMPLATES.ONBOARDING_DAY7.template, templateParams);
    } catch (error) {
      console.error('Day 7 email error:', error);
    }
  }

  // Helper methods for content generation
  private getWelcomeMessage(name: string): string {
    return `
      Olá ${name}!
      
      Seja muito bem-vindo(a) ao AffiliateFlow Premium! 🎉
      
      Você acabou de dar o primeiro passo rumo à sua independência financeira.
      
      Nos próximos minutos, você vai descobrir exatamente como transformar
      indicações simples em uma renda recorrente de R$ 15.000/mês.
    `;
  }

  private getBonusContent(): string {
    return `
      🎁 **BÔNUS EXCLUSIVOS PARA VOCÊ:**
      
      ✅ E-book: "7 Segredos dos Afiliados Milionários"
      ✅ Planilha de Controle de Comissões
      ✅ Scripts Prontos para Redes Sociais
      ✅ Vídeo: "Primeira Venda em 24h"
      ✅ Acesso ao Grupo VIP no Telegram
    `;
  }

  private getNextSteps(): string {
    return `
      📋 **SEUS PRÓXIMOS PASSOS:**
      
      1️⃣ Confirme seu email (se ainda não fez)
      2️⃣ Acesse seu painel de afiliado
      3️⃣ Baixe os materiais exclusivos
      4️⃣ Faça sua primeira indicação
      5️⃣ Acompanhe suas comissões em tempo real
    `;
  }

  private getSocialProof(): string {
    return `
      🏆 **RESULTADOS REAIS DOS NOSSOS AFILIADOS:**
      
      "Em 30 dias faturei R$ 12.847 só com indicações!" - Ana Paula, SP
      "Método simples que realmente funciona. R$ 8.500 no primeiro mês!" - Carlos, RJ  
      "Nunca pensei que fosse tão fácil. R$ 15.200 em 45 dias!" - Mariana, MG
    `;
  }
}

// Initialize email marketing service
export const emailMarketing = new EmailMarketingService();
